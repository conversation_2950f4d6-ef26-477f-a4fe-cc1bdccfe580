import { PowerDialer } from './PowerDialer.js'

export class ColdCallingApp {
  constructor() {
    this.companies = JSON.parse(localStorage.getItem('companies')) || []
    this.currentFilter = 'all'
    this.currentCityFilter = 'all'
    this.currentStateFilter = 'all'
    this.currentView = 'directory' // 'directory' or 'dialer' - default to directory
    this.powerDialer = null

    // Update existing companies to include city/state if missing
    this.updateExistingCompaniesWithCityState()
  }

  init() {
    // If we have companies and default view is dialer, initialize power dialer
    if (this.companies.length > 0 && this.currentView === 'dialer') {
      this.initializePowerDialer()
    }
    this.render()
    this.attachEventListeners()
  }

  render() {
    const app = document.querySelector('#app')
    const callClicks = localStorage.getItem('googleVoiceClicks') || 0

    if (this.currentView === 'dialer' && this.powerDialer) {
      app.innerHTML = this.powerDialer.render()
      this.powerDialer.attachEventListeners()
      return
    }

    app.innerHTML = `
      <div class="cold-calling-app">
        <header class="app-header">
          <h1>Cold Calling Directory</h1>
          <p>Pressure Washing Companies</p>
        </header>

        <div class="upload-section">
          <h2>Upload Company Data</h2>
          <input type="file" id="jsonUpload" accept=".json" />
          <button id="uploadBtn">Upload JSON File</button>
          <div class="click-tracker">
            <p><strong>Google Voice Clicks:</strong> <span id="gvClickCount">${callClicks}</span></p>
            <button id="resetClickCounter" class="reset-btn">Reset Counter</button>
          </div>
        </div>

        <div class="view-controls">
          <button id="directoryView" class="view-btn ${this.currentView === 'directory' ? 'active' : ''}">
            📋 Directory View
          </button>
          <button id="dialerView" class="view-btn ${this.currentView === 'dialer' ? 'active' : ''}">
            📞 Power Dialer
          </button>
        </div>

        <div class="filter-section">
          <h3>Filter Companies</h3>
          <div class="filter-controls">
            <div class="filter-group">
              <label>Status:</label>
              <select id="statusFilter">
                <option value="all" ${this.currentFilter === 'all' ? 'selected' : ''}>All Statuses</option>
                <option value="not-called" ${this.currentFilter === 'not-called' ? 'selected' : ''}>Not Called</option>
                <option value="not-available" ${this.currentFilter === 'not-available' ? 'selected' : ''}>Not Available</option>
                <option value="not-interested" ${this.currentFilter === 'not-interested' ? 'selected' : ''}>Not Interested</option>
                <option value="voicemail" ${this.currentFilter === 'voicemail' ? 'selected' : ''}>Voicemail</option>
                <option value="wrong-number" ${this.currentFilter === 'wrong-number' ? 'selected' : ''}>Wrong Number</option>
                <option value="call-back" ${this.currentFilter === 'call-back' ? 'selected' : ''}>Call Back</option>
                <option value="sold" ${this.currentFilter === 'sold' ? 'selected' : ''}>Sold</option>
              </select>
            </div>

            <div class="filter-group">
              <label>City:</label>
              <select id="cityFilter">
                <option value="all" ${this.currentCityFilter === 'all' ? 'selected' : ''}>All Cities</option>
                ${this.getCityOptions()}
              </select>
            </div>

            <div class="filter-group">
              <label>State:</label>
              <select id="stateFilter">
                <option value="all" ${this.currentStateFilter === 'all' ? 'selected' : ''}>All States</option>
                ${this.getStateOptions()}
              </select>
            </div>
          </div>
        </div>

        <div class="companies-section">
          <h3>Companies (${this.getFilteredCompanies().length})</h3>
          <div id="companiesList" class="companies-list">
            ${this.renderCompanies()}
          </div>
        </div>
      </div>
    `
  }

  renderCompanies() {
    const filteredCompanies = this.getFilteredCompanies()
    
    if (filteredCompanies.length === 0) {
      return '<p class="no-companies">No companies found. Upload a JSON file to get started.</p>'
    }

    return filteredCompanies.map((company, index) => `
      <div class="company-card ${this.getCardColorClass(company.disposition)}" data-company-id="${company.id}" data-index="${index}">
        <div class="company-info">
          <h4>${company.title || 'Unknown Company'}</h4>
          <div class="contact-details">
            <p><strong>Phone:</strong> ${company.phone || 'N/A'}</p>
            <p><strong>Website:</strong> ${company.website ? `<a href="${company.website}" target="_blank" onclick="event.stopPropagation()">${this.truncateUrl(company.website)}</a>` : 'N/A'}</p>
            <p><strong>Email:</strong> ${company.email || 'N/A'}</p>
            <p><strong>Contact:</strong> ${company.contactName || 'N/A'}</p>
            <p><strong>Address:</strong> ${company.originalData?.address || 'N/A'}</p>
          </div>
          <div class="card-action">
            <span class="view-in-dialer">📞 View in Power Dialer</span>
          </div>
        </div>
        
        <div class="call-management">
          <div class="disposition-section">
            <label>Disposition:</label>
            <select class="disposition-select" data-company-id="${company.id}">
              <option value="">Select Status</option>
              <option value="not-available" ${company.disposition === 'not-available' ? 'selected' : ''}>Not Available</option>
              <option value="not-interested" ${company.disposition === 'not-interested' ? 'selected' : ''}>Not Interested</option>
              <option value="voicemail" ${company.disposition === 'voicemail' ? 'selected' : ''}>Voicemail</option>
              <option value="wrong-number" ${company.disposition === 'wrong-number' ? 'selected' : ''}>Wrong Number</option>
              <option value="call-back" ${company.disposition === 'call-back' ? 'selected' : ''}>Call Back</option>
              <option value="sold" ${company.disposition === 'sold' ? 'selected' : ''}>Sold</option>
            </select>
          </div>
          
          <div class="notes-section">
            <label>Call Notes:</label>
            <textarea class="call-notes" data-company-id="${company.id}" placeholder="Add notes about your call...">${company.notes || ''}</textarea>
            <button class="save-notes-btn" data-company-id="${company.id}">Save Notes</button>
          </div>
        </div>
      </div>
    `).join('')
  }

  getFilteredCompanies() {
    let filtered = this.companies

    // Filter by status
    if (this.currentFilter !== 'all') {
      if (this.currentFilter === 'not-called') {
        filtered = filtered.filter(company => !company.disposition)
      } else {
        filtered = filtered.filter(company => company.disposition === this.currentFilter)
      }
    }

    // Filter by city
    if (this.currentCityFilter !== 'all') {
      filtered = filtered.filter(company => company.city === this.currentCityFilter)
    }

    // Filter by state
    if (this.currentStateFilter !== 'all') {
      filtered = filtered.filter(company => company.state === this.currentStateFilter)
    }

    return filtered
  }

  attachEventListeners() {
    // File upload
    document.getElementById('uploadBtn').addEventListener('click', () => this.handleFileUpload())

    // Reset click counter
    const resetBtn = document.getElementById('resetClickCounter')
    if (resetBtn) {
      resetBtn.addEventListener('click', () => {
        PowerDialer.resetGoogleVoiceClickCounter()
      })
    }

    // View switching
    document.getElementById('directoryView').addEventListener('click', () => this.switchToDirectory())
    document.getElementById('dialerView').addEventListener('click', () => this.switchToDialer())

    // Filter changes
    document.getElementById('statusFilter').addEventListener('change', (e) => {
      this.currentFilter = e.target.value
      this.updateCompaniesList()
    })

    document.getElementById('cityFilter').addEventListener('change', (e) => {
      this.currentCityFilter = e.target.value
      this.updateCompaniesList()
    })

    document.getElementById('stateFilter').addEventListener('change', (e) => {
      this.currentStateFilter = e.target.value
      this.updateCompaniesList()
    })

    // Disposition changes
    document.addEventListener('change', (e) => {
      if (e.target.classList.contains('disposition-select')) {
        this.updateDisposition(e.target.dataset.companyId, e.target.value)
      }
    })

    // Save notes
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('save-notes-btn')) {
        const companyId = e.target.dataset.companyId
        const notesTextarea = document.querySelector(`textarea[data-company-id="${companyId}"]`)
        this.updateNotes(companyId, notesTextarea.value)
      }
    })

    // Click to view in Power Dialer
    document.addEventListener('click', (e) => {
      const card = e.target.closest('.company-card')
      if (card && !e.target.closest('a') && !e.target.closest('select') && !e.target.closest('button') && !e.target.closest('textarea')) {
        const companyId = card.dataset.companyId
        this.openInPowerDialer(companyId)
      }
    })
  }

  handleFileUpload() {
    const fileInput = document.getElementById('jsonUpload')
    const file = fileInput.files[0]
    
    if (!file) {
      alert('Please select a JSON file')
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const jsonData = JSON.parse(e.target.result)
        this.processUploadedData(jsonData)
      } catch (error) {
        alert('Error parsing JSON file: ' + error.message)
      }
    }
    reader.readAsText(file)
  }

  processUploadedData(jsonData) {
    const newCompanies = jsonData.map((item, index) => {
      // Use existing city/state fields if available, otherwise parse from address
      let city = item.city || null
      let state = item.state || null

      // If city/state are missing, try to parse from address
      if (!city || !state) {
        const parsed = this.parseAddress(item.address)
        city = city || parsed.city
        state = state || parsed.state
      }

      // Handle full state names (convert "North Carolina" to "NC")
      if (state) {
        state = this.normalizeStateName(state)
      }

      return {
        id: `company_${Date.now()}_${index}`,
        title: item.title,
        phone: item.phone,
        website: item.website,
        email: item.email || null,
        contactName: item.contactName || null,
        city: city,
        state: state,
        disposition: null,
        notes: '',
        originalData: item
      }
    })

    this.companies = [...this.companies, ...newCompanies]
    this.saveToLocalStorage()

    // Re-render the entire view to update dropdowns with new cities/states
    this.render()
    this.attachEventListeners()

    alert(`Successfully uploaded ${newCompanies.length} companies`)
  }

  parseAddress(address) {
    // Handle null, undefined, empty, or "\N" addresses
    if (!address || address === '\\N' || address.trim() === '') {
      return { city: null, state: null }
    }

    // Common address formats:
    // "1300 Burtonwood Cir, Charlotte, NC 28212"
    // "Charlotte, NC 28212"
    // "Charlotte, NC"
    // "Phoenix AZ 85001" (without commas)
    // "123 Main St Phoenix AZ 85001"

    // First try comma-separated format
    const parts = address.split(',').map(part => part.trim())

    if (parts.length >= 2) {
      // Get the last part which should contain state (and possibly zip)
      const lastPart = parts[parts.length - 1]
      // Get the second to last part which should be the city
      const cityPart = parts[parts.length - 2]

      // Extract state (first 2 letters of last part)
      const stateMatch = lastPart.match(/^([A-Z]{2})/i)
      const state = stateMatch ? stateMatch[1].toUpperCase() : null

      // City is the second to last part
      const city = cityPart || null

      return { city, state }
    }

    // If no commas, try space-separated format (common in some datasets)
    // Look for pattern: "City ST 12345" or "City ST"
    const spaceMatch = address.match(/\b([A-Za-z\s]+?)\s+([A-Z]{2})\s*(\d{5})?/i)
    if (spaceMatch) {
      const city = spaceMatch[1].trim()
      const state = spaceMatch[2].toUpperCase()
      return { city, state }
    }

    return { city: null, state: null }
  }

  normalizeStateName(state) {
    if (!state) return null

    // If already 2 letters, return as-is (uppercase)
    if (state.length === 2) {
      return state.toUpperCase()
    }

    // Map of full state names to abbreviations
    const stateMap = {
      'alabama': 'AL', 'alaska': 'AK', 'arizona': 'AZ', 'arkansas': 'AR', 'california': 'CA',
      'colorado': 'CO', 'connecticut': 'CT', 'delaware': 'DE', 'florida': 'FL', 'georgia': 'GA',
      'hawaii': 'HI', 'idaho': 'ID', 'illinois': 'IL', 'indiana': 'IN', 'iowa': 'IA',
      'kansas': 'KS', 'kentucky': 'KY', 'louisiana': 'LA', 'maine': 'ME', 'maryland': 'MD',
      'massachusetts': 'MA', 'michigan': 'MI', 'minnesota': 'MN', 'mississippi': 'MS', 'missouri': 'MO',
      'montana': 'MT', 'nebraska': 'NE', 'nevada': 'NV', 'new hampshire': 'NH', 'new jersey': 'NJ',
      'new mexico': 'NM', 'new york': 'NY', 'north carolina': 'NC', 'north dakota': 'ND', 'ohio': 'OH',
      'oklahoma': 'OK', 'oregon': 'OR', 'pennsylvania': 'PA', 'rhode island': 'RI', 'south carolina': 'SC',
      'south dakota': 'SD', 'tennessee': 'TN', 'texas': 'TX', 'utah': 'UT', 'vermont': 'VT',
      'virginia': 'VA', 'washington': 'WA', 'west virginia': 'WV', 'wisconsin': 'WI', 'wyoming': 'WY'
    }

    const normalized = stateMap[state.toLowerCase()]
    return normalized || state.toUpperCase()
  }

  updateDisposition(companyId, disposition) {
    const company = this.companies.find(c => c.id === companyId)
    if (company) {
      company.disposition = disposition
      this.saveToLocalStorage()
    }
  }

  updateNotes(companyId, notes) {
    const company = this.companies.find(c => c.id === companyId)
    if (company) {
      company.notes = notes
      this.saveToLocalStorage()
      alert('Notes saved successfully')
    }
  }

  updateCompaniesList() {
    const companiesList = document.getElementById('companiesList')
    if (companiesList) {
      companiesList.innerHTML = this.renderCompanies()
    }

    // Update count
    const countElement = document.querySelector('.companies-section h3')
    if (countElement) {
      countElement.textContent = `Companies (${this.getFilteredCompanies().length})`
    }

    // Update filter dropdowns to reflect current state
    this.updateFilterDropdowns()
  }

  updateFilterDropdowns() {
    // Update city filter options
    const cityFilter = document.getElementById('cityFilter')
    if (cityFilter) {
      cityFilter.innerHTML = `
        <option value="all" ${this.currentCityFilter === 'all' ? 'selected' : ''}>All Cities</option>
        ${this.getCityOptions()}
      `
    }

    // Update state filter options
    const stateFilter = document.getElementById('stateFilter')
    if (stateFilter) {
      stateFilter.innerHTML = `
        <option value="all" ${this.currentStateFilter === 'all' ? 'selected' : ''}>All States</option>
        ${this.getStateOptions()}
      `
    }
  }

  saveToLocalStorage() {
    localStorage.setItem('companies', JSON.stringify(this.companies))
  }

  switchToDirectory() {
    this.currentView = 'directory'
    this.render()
    this.attachEventListeners()
  }

  switchToDialer() {
    if (this.companies.length === 0) {
      alert('Please upload company data first')
      return
    }

    this.currentView = 'dialer'
    this.initializePowerDialer()
    this.render()
    // Note: attachEventListeners is called in render() for dialer view
  }

  initializePowerDialer() {
    const filteredCompanies = this.getFilteredCompanies()

    if (filteredCompanies.length === 0) {
      this.powerDialer = new PowerDialer(
        [],
        (company) => this.handleCompanyUpdate(company),
        () => this.switchToDirectory(),
        0
      )
      return
    }

    // Get last position before creating PowerDialer
    const lastPosition = this.getLastDialerPosition()
    const startIndex = (lastPosition !== null && lastPosition < filteredCompanies.length) ? lastPosition : 0

    this.powerDialer = new PowerDialer(
      filteredCompanies,
      (company) => this.handleCompanyUpdate(company),
      () => this.switchToDirectory(),
      startIndex // Pass the starting index to constructor
    )
  }

  handleCompanyUpdate(updatedCompany) {
    const index = this.companies.findIndex(c => c.id === updatedCompany.id)
    if (index !== -1) {
      this.companies[index] = updatedCompany
      this.saveToLocalStorage()
    }
  }

  getCardColorClass(disposition) {
    switch(disposition) {
      case 'not-available': return 'card-not-available'
      case 'not-interested': return 'card-not-interested'
      case 'voicemail': return 'card-voicemail'
      case 'wrong-number': return 'card-wrong-number'
      case 'call-back': return 'card-call-back'
      case 'sold': return 'card-sold'
      default: return ''
    }
  }

  saveDialerPosition(index) {
    localStorage.setItem('dialerPosition', index.toString())
  }

  getLastDialerPosition() {
    const position = localStorage.getItem('dialerPosition')
    return position !== null ? parseInt(position, 10) : null
  }

  truncateUrl(url) {
    if (!url) return 'N/A'
    // Remove protocol and www
    let display = url.replace(/^https?:\/\/(www\.)?/, '')
    // Truncate for directory view - shorter than Power Dialer
    if (display.length > 25) {
      display = display.substring(0, 22) + '...'
    }
    return display
  }

  openInPowerDialer(companyId) {
    // Find the company index in the current filtered list
    const filteredCompanies = this.getFilteredCompanies()
    const companyIndex = filteredCompanies.findIndex(company => company.id === companyId)

    if (companyIndex !== -1) {
      // Save the position we want to jump to
      localStorage.setItem('dialerPosition', companyIndex.toString())

      // Switch to Power Dialer view
      this.switchToDialer()
    }
  }

  getCityOptions() {
    const cities = [...new Set(this.companies.map(company => company.city).filter(city => city))]
    return cities.sort().map(city =>
      `<option value="${city}" ${this.currentCityFilter === city ? 'selected' : ''}>${city}</option>`
    ).join('')
  }

  getStateOptions() {
    const states = [...new Set(this.companies.map(company => company.state).filter(state => state))]
    return states.sort().map(state =>
      `<option value="${state}" ${this.currentStateFilter === state ? 'selected' : ''}>${state}</option>`
    ).join('')
  }

  updateExistingCompaniesWithCityState() {
    let updated = false

    this.companies.forEach(company => {
      // Update if city/state are missing
      if (company.city === undefined || company.state === undefined ||
          (!company.city && !company.state)) {

        // First try to use direct city/state fields from originalData
        let city = company.originalData?.city || null
        let state = company.originalData?.state || null

        // If still missing, try to parse from address
        if (!city || !state) {
          const parsed = this.parseAddress(company.originalData?.address)
          city = city || parsed.city
          state = state || parsed.state
        }

        // Normalize state name
        if (state) {
          state = this.normalizeStateName(state)
        }

        company.city = city
        company.state = state
        updated = true
      }
    })

    // Save to localStorage if any companies were updated
    if (updated) {
      this.saveToLocalStorage()
    }
  }

  // Method to force re-parse all addresses (useful for debugging)
  reParseAllAddresses() {
    let updated = 0

    this.companies.forEach(company => {
      // Use direct city/state fields if available, otherwise parse from address
      let city = company.originalData?.city || null
      let state = company.originalData?.state || null

      // If city/state are missing, try to parse from address
      if (!city || !state) {
        const parsed = this.parseAddress(company.originalData?.address)
        city = city || parsed.city
        state = state || parsed.state
      }

      // Normalize state name
      if (state) {
        state = this.normalizeStateName(state)
      }

      if (city !== company.city || state !== company.state) {
        company.city = city
        company.state = state
        updated++
      }
    })

    if (updated > 0) {
      this.saveToLocalStorage()
      this.render()
      this.attachEventListeners()
      console.log(`Re-parsed ${updated} company addresses`)
    }

    return updated
  }
}
